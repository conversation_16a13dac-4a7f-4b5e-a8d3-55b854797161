#!/bin/bash
# Test Worker Service Script
# Script để test worker service hoạt động đúng

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with color
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if container is running
check_container() {
    local container_name=$1
    if docker ps | grep -q "$container_name"; then
        success "$container_name is running"
        return 0
    else
        error "$container_name is not running"
        return 1
    fi
}

# Function to check worker health
check_worker_health() {
    log "Checking worker health..."
    
    if docker exec greennation-worker /usr/local/bin/health-check.sh; then
        success "Worker health check passed"
    else
        error "Worker health check failed"
        return 1
    fi
}

# Function to check supervisor processes
check_supervisor() {
    log "Checking supervisor processes..."
    
    local status_output
    status_output=$(docker exec greennation-worker supervisorctl status 2>/dev/null || echo "ERROR")
    
    if [ "$status_output" = "ERROR" ]; then
        error "Cannot get supervisor status"
        return 1
    fi
    
    echo "$status_output"
    
    # Check if all required processes are running
    local required_processes=("messenger-async" "messenger-redis-1" "messenger-redis-2")
    local failed=0
    
    for process in "${required_processes[@]}"; do
        if echo "$status_output" | grep -q "$process.*RUNNING"; then
            success "$process is running"
        else
            error "$process is not running"
            failed=1
        fi
    done
    
    return $failed
}

# Function to test message dispatch
test_message_dispatch() {
    log "Testing message dispatch..."
    
    # Test schedule message
    log "Dispatching test schedule message..."
    docker exec greennation-web-test php bin/console messenger:dispatch-message "App\\Message\\ScheduleMessage" '{"scheduleJobId": 1}' || warning "Schedule message dispatch failed (expected if no job with ID 1)"
    
    # Check message stats
    log "Checking message stats..."
    docker exec greennation-worker php bin/console messenger:stats
    
    success "Message dispatch test completed"
}

# Function to check logs
check_logs() {
    log "Checking worker logs..."
    
    echo "=== Supervisor logs ==="
    docker exec greennation-worker tail -20 /var/log/supervisor/supervisord.log || warning "No supervisor logs yet"
    
    echo "=== Messenger async logs ==="
    docker exec greennation-worker tail -20 /var/log/supervisor/messenger-async.out.log || warning "No async logs yet"
    
    echo "=== Application logs ==="
    docker exec greennation-worker tail -20 /var/www/html/var/log/dev.log || warning "No application logs yet"
}

# Function to start worker stack
start_worker() {
    log "Starting worker service stack..."
    
    # Stop existing containers
    docker-compose -f docker-compose-worker.yml down
    
    # Build and start
    docker-compose -f docker-compose-worker.yml up -d --build
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Check if all containers are running
    local containers=("greennation-worker" "greennation-worker-db" "greennation-worker-redis")
    for container in "${containers[@]}"; do
        check_container "$container" || return 1
    done
    
    success "Worker stack started successfully"
}

# Function to stop worker stack
stop_worker() {
    log "Stopping worker service stack..."
    docker-compose -f docker-compose-worker.yml down
    success "Worker stack stopped"
}

# Function to show worker status
show_status() {
    log "Worker Service Status:"
    echo ""
    
    # Container status
    echo "=== Container Status ==="
    docker-compose -f docker-compose-worker.yml ps
    echo ""
    
    # Worker health
    if check_container "greennation-worker"; then
        check_worker_health
        echo ""
        check_supervisor
    fi
    echo ""
    
    # Redis status
    echo "=== Redis Status ==="
    if check_container "greennation-worker-redis"; then
        docker exec greennation-worker-redis redis-cli ping || warning "Redis not responding"

        # Check Redis from worker perspective
        echo "Checking Redis connectivity from worker..."
        docker exec greennation-worker php -r "
        try {
            \$redis = new Redis();
            \$redis->connect('redis', 6379, 5);
            echo 'Worker -> Redis: ' . (\$redis->ping() ? 'OK' : 'FAIL') . PHP_EOL;
        } catch (Exception \$e) {
            echo 'Worker -> Redis: FAIL - ' . \$e->getMessage() . PHP_EOL;
        }
        " || warning "Worker cannot connect to Redis"
    else
        warning "Redis container not running"
    fi
    echo ""
    
    # Database status
    echo "=== Database Status ==="
    docker exec greennation-worker-db mysqladmin ping -h localhost -u root -proot || warning "Database not responding"
}

# Function to run full test
run_full_test() {
    log "Running full worker test..."
    
    start_worker || return 1
    
    log "Waiting for worker to be fully ready..."
    sleep 60
    
    show_status
    
    if check_container "greennation-web-test"; then
        test_message_dispatch
    else
        warning "Web container not running, skipping message dispatch test"
    fi
    
    check_logs
    
    success "Full worker test completed"
}

# Main function
main() {
    case "${1:-}" in
        "start")
            start_worker
            ;;
        "stop")
            stop_worker
            ;;
        "status")
            show_status
            ;;
        "test")
            run_full_test
            ;;
        "logs")
            check_logs
            ;;
        "health")
            check_worker_health
            ;;
        *)
            echo "Usage: $0 {start|stop|status|test|logs|health}"
            echo ""
            echo "Commands:"
            echo "  start   - Start worker service stack"
            echo "  stop    - Stop worker service stack"
            echo "  status  - Show worker status"
            echo "  test    - Run full worker test"
            echo "  logs    - Show worker logs"
            echo "  health  - Check worker health"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
