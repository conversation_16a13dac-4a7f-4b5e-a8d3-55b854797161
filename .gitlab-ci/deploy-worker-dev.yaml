# GitLab CI/CD Pipeline for Worker Service
# Tách riêng khỏi web application pipeline

variables:
  ## SYSTEM
  ENV: dev
  SERVICE_NAME: gn-worker-service
  SERVICE_CPU_REQ: 512m
  SERVICE_MEM_REQ: 1Gi
  SERVICE_CPU_LIMIT: 1000m
  SERVICE_MEM_LIMIT: 2Gi
  SERVICE_REPLICAS: 2
  
  ## GOOGLE CLOUD
  GCLOUD_PROJECT: green-nation-dev-5004
  IMAGE_REPO: europe-west9-docker.pkg.dev
  IMAGE_NAME: ${IMAGE_REPO}/${GCLOUD_PROJECT}/appv2/${SERVICE_NAME}
  IMAGE_TAG: $ENV-$CI_COMMIT_SHA-$CI_PIPELINE_ID
  
  ## KUBERNETES
  K8S_NAMESPACE: default
  K8S_DEPLOYMENT_NAME: ${SERVICE_NAME}

default:
  tags:
    - dev_gke

stages:
  - build-worker-image
  - deploy-worker

# Build Worker Docker Image
build_worker_image:
  stage: build-worker-image
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  before_script: []
  script:
    - echo "🏗️ Building worker Docker image..."
    - echo "Image: ${IMAGE_NAME}:${IMAGE_TAG}"
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/devops/worker/Dockerfile"
      --destination "${IMAGE_NAME}:${IMAGE_TAG}"
      --snapshot-mode=redo
      --cache=true
      --cache-ttl=24h
  rules:
    - if: '$CI_COMMIT_BRANCH == "deploy/dev"'
      when: manual
    - if: "$CI_COMMIT_TAG"
      when: manual
  artifacts:
    reports:
      dotenv: build.env
  after_script:
    - echo "IMAGE_TAG=${IMAGE_TAG}" > build.env
    - echo "✅ Worker image built successfully"

# Deploy Worker to Kubernetes
deploy_worker:
  stage: deploy-worker
  image: google/cloud-sdk:latest
  variables:
    GIT_STRATEGY: none
  before_script:
    - echo "🚀 Deploying worker service..."
    - echo "Environment: ${ENV}"
    - echo "Service: ${SERVICE_NAME}"
    - echo "Image: ${IMAGE_NAME}:${IMAGE_TAG}"
  script:
    # Get deployment script from Google Cloud Secret
    - gcloud secrets versions access latest --secret=gitlab-runner-deploy-dev --project="${GCLOUD_PROJECT}" > /tmp/deploy_script.sh
    - chmod +x /tmp/deploy_script.sh
    
    # Create worker deployment YAML
    - |
      cat > /tmp/worker-deployment.yaml << EOF
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: ${K8S_DEPLOYMENT_NAME}
        namespace: ${K8S_NAMESPACE}
        labels:
          app: ${SERVICE_NAME}
          version: ${IMAGE_TAG}
      spec:
        replicas: ${SERVICE_REPLICAS}
        selector:
          matchLabels:
            app: ${SERVICE_NAME}
        template:
          metadata:
            labels:
              app: ${SERVICE_NAME}
              version: ${IMAGE_TAG}
          spec:
            containers:
            - name: worker
              image: ${IMAGE_NAME}:${IMAGE_TAG}
              resources:
                requests:
                  memory: "${SERVICE_MEM_REQ}"
                  cpu: "${SERVICE_CPU_REQ}"
                limits:
                  memory: "${SERVICE_MEM_LIMIT}"
                  cpu: "${SERVICE_CPU_LIMIT}"
              env:
              - name: APP_ENV
                value: "prod"
              - name: APP_DEBUG
                value: "0"
              envFrom:
              - secretRef:
                  name: greennation-secrets
              livenessProbe:
                exec:
                  command:
                  - /usr/local/bin/health-check.sh
                initialDelaySeconds: 60
                periodSeconds: 30
                timeoutSeconds: 10
                failureThreshold: 3
              readinessProbe:
                exec:
                  command:
                  - /usr/local/bin/health-check.sh
                initialDelaySeconds: 30
                periodSeconds: 10
                timeoutSeconds: 5
                failureThreshold: 3
              startupProbe:
                exec:
                  command:
                  - /usr/local/bin/health-check.sh
                initialDelaySeconds: 10
                periodSeconds: 10
                timeoutSeconds: 5
                failureThreshold: 30
            restartPolicy: Always
      EOF
    
    # Apply deployment
    - kubectl apply -f /tmp/worker-deployment.yaml
    
    # Wait for rollout to complete
    - kubectl rollout status deployment/${K8S_DEPLOYMENT_NAME} -n ${K8S_NAMESPACE} --timeout=600s
    
    # Verify deployment
    - kubectl get pods -l app=${SERVICE_NAME} -n ${K8S_NAMESPACE}
    - kubectl logs -l app=${SERVICE_NAME} -n ${K8S_NAMESPACE} --tail=50
    
    - echo "✅ Worker deployment completed successfully"
  rules:
    - if: '$CI_COMMIT_BRANCH == "deploy/dev"'
      when: manual
  needs: ["build_worker_image"]
  dependencies:
    - build_worker_image

# Optional: Rollback worker deployment
rollback_worker:
  stage: deploy-worker
  image: google/cloud-sdk:latest
  variables:
    GIT_STRATEGY: none
  script:
    - echo "🔄 Rolling back worker deployment..."
    - kubectl rollout undo deployment/${K8S_DEPLOYMENT_NAME} -n ${K8S_NAMESPACE}
    - kubectl rollout status deployment/${K8S_DEPLOYMENT_NAME} -n ${K8S_NAMESPACE} --timeout=300s
    - echo "✅ Worker rollback completed"
  rules:
    - if: '$CI_COMMIT_BRANCH == "deploy/dev"'
      when: manual
  allow_failure: true
