[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
user=www-data
loglevel=info

[program:messenger-async]
command=php /var/www/html/bin/console messenger:consume async -vv --time-limit=3600 --memory-limit=512M
directory=/var/www/html
user=www-data
numprocs=2
process_name=%(program_name)s_%(process_num)02d
autorestart=true
autostart=true
startretries=3
stdout_logfile=/var/log/supervisor/messenger-async.out.log
stderr_logfile=/var/log/supervisor/messenger-async.err.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_backups=5

[program:messenger-redis-1]
command=php /var/www/html/bin/console messenger:consume async_redis_1 -vv --time-limit=3600 --memory-limit=512M
directory=/var/www/html
user=www-data
numprocs=1
autorestart=true
autostart=true
startretries=3
stdout_logfile=/var/log/supervisor/messenger-redis-1.out.log
stderr_logfile=/var/log/supervisor/messenger-redis-1.err.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_backups=5

[program:messenger-redis-2]
command=php /var/www/html/bin/console messenger:consume async_redis_2 -vv --time-limit=3600 --memory-limit=512M
directory=/var/www/html
user=www-data
numprocs=1
autorestart=true
autostart=true
startretries=3
stdout_logfile=/var/log/supervisor/messenger-redis-2.out.log
stderr_logfile=/var/log/supervisor/messenger-redis-2.err.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_backups=5

[group:messenger-workers]
programs=messenger-async,messenger-redis-1,messenger-redis-2
priority=999

[eventlistener:graceful-shutdown]
command=php /var/www/html/bin/console messenger:stop-workers
events=PROCESS_STATE_STOPPING,PROCESS_STATE_EXITED,PROCESS_STATE_FATAL
user=www-data
autorestart=false
autostart=false