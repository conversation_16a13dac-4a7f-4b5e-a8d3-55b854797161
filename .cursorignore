# Dependencies
/vendor/
/node_modules/
composer.lock
package-lock.json
yarn.lock

# Cache and logs
/var/cache/
/var/log/
/var/sessions/
*.log

# Environment files
.env
.env.local
.env.*.local

# Build artifacts
/public/build/
/public/bundles/
/public/assets/
/assets/built/

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Temporary files
/tmp/
/temp/

# Symfony specific
/config/secrets/
/public/uploads/
/translations/*.xlf
/translations/*.po
/translations/*.pot

# API Platform specific
/api/docs/
/public/docs/
/var/cache/api_platform/
*.openapi.json
*.openapi.yaml
/config/api_platform/
/var/api_platform_cache/

# Database
*.db
*.sqlite
*.sqlite3

# PHPUnit
/phpunit.xml
/.phpunit.result.cache
/coverage/

# Webpack Encore
/var/webpack/
/public/js/
/public/css/

# Doctrine migrations (optional - comment out if you want to include them)
# /migrations/

# Backup files
*.bak
*.backup
*.orig

# Lock files
*.lock

devops/local/nginx/logs
devops/local/database

devops/local/nginx/logs/*
devops/local/database/*
