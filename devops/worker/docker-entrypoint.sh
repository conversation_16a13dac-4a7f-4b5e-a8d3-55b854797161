#!/bin/bash
# Worker Service Entrypoint Script
# Setup proper permissions và environment cho worker

set -e

echo "🚀 Starting Worker Service..."

# Create necessary directories if they don't exist
echo "📁 Creating directories..."
mkdir -p /var/www/html/var/cache
mkdir -p /var/www/html/var/log
mkdir -p /var/log/supervisor

# Set proper permissions for Symfony cache and logs
echo "🔐 Setting permissions..."
chown -R www-data:www-data /var/www/html/var
chmod -R 755 /var/www/html/var/cache
chmod -R 755 /var/www/html/var/log

# Set permissions for supervisor logs
chown -R www-data:www-data /var/log/supervisor
chmod -R 755 /var/log/supervisor

# Clear Symfony cache for worker environment
echo "🧹 Clearing Symfony cache..."
if [ "$APP_ENV" = "prod" ]; then
    php /var/www/html/bin/console cache:clear --env=prod --no-debug
else
    php /var/www/html/bin/console cache:clear --env=dev
fi

# Warm up cache
echo "🔥 Warming up cache..."
if [ "$APP_ENV" = "prod" ]; then
    php /var/www/html/bin/console cache:warmup --env=prod --no-debug
else
    php /var/www/html/bin/console cache:warmup --env=dev
fi

# Check database connectivity
echo "🔍 Checking database connectivity..."
php /var/www/html/bin/console doctrine:query:sql "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Check Redis connectivity for messenger transports
echo "🔍 Checking Redis connectivity..."
php -r "
try {
    \$redis = new Redis();
    \$dsn = parse_url(getenv('MESSENGER_TRANSPORT_DSN') ?: 'redis://localhost:6379');
    \$redis->connect(\$dsn['host'] ?: 'localhost', \$dsn['port'] ?: 6379);
    \$redis->ping();
    echo 'Redis connection successful\n';
} catch (Exception \$e) {
    echo 'Redis connection failed: ' . \$e->getMessage() . '\n';
    exit(1);
}
"

# Check messenger transports
echo "🔍 Checking messenger transports..."
php /var/www/html/bin/console messenger:stats > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Messenger transports ready"
else
    echo "❌ Messenger transports not ready"
    exit 1
fi

# Display worker configuration
echo "📊 Worker Configuration:"
echo "  - APP_ENV: ${APP_ENV:-dev}"
echo "  - PHP Memory Limit: $(php -r 'echo ini_get("memory_limit");')"
echo "  - Available transports:"
php /var/www/html/bin/console messenger:stats 2>/dev/null | grep -E "async|redis" || echo "    No transport info available"

# Show supervisor configuration
echo "📋 Supervisor will manage these processes:"
echo "  - messenger-async (2 processes)"
echo "  - messenger-redis-1 (1 process)"
echo "  - messenger-redis-2 (1 process)"

echo "✅ Worker initialization complete!"
echo "🎯 Starting supervisor..."

# Execute the command provided as arguments
exec "$@"
