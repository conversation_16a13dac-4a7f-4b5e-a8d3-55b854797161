# Worker Service Dockerfile
FROM europe-west9-docker.pkg.dev/green-nation-dev-5004/appv2/gn-php-base-service:latest

RUN apt-get update && apt-get install -y \
    supervisor \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

COPY --chown=www-data:www-data . /var/www/html/

RUN cd /var/www/html && composer install --no-interaction --optimize-autoloader --no-dev --no-scripts

RUN mkdir -p /var/log/supervisor \
    && mkdir -p /var/www/html/var/cache \
    && mkdir -p /var/www/html/var/log \
    && chown -R www-data:www-data /var/www/html/var \
    && chown -R www-data:www-data /var/log/supervisor

COPY devops/worker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

COPY devops/worker/docker-entrypoint.sh /usr/local/bin/worker-entrypoint.sh
RUN chmod +x /usr/local/bin/worker-entrypoint.sh

COPY devops/worker/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

WORKDIR /var/www/html/

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

USER www-data

ENTRYPOINT ["/usr/local/bin/worker-entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf", "-n"]
