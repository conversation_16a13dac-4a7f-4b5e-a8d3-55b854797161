# 🚀 GreenNation Worker Service

## 📋 Overview

Worker service đã được tách riêng khỏi web application để xử lý background jobs thông qua Symfony Messenger và Google Cloud Pub/Sub. Service này sử dụng supervisor để quản lý multiple worker processes.

## 🏗️ Architecture

```
Google Cloud Scheduler → Pub/Sub → Webhook Endpoint → Symfony Messenger → Worker Service
```

### Worker Components
- **Supervisor**: Quản lý worker processes
- **3 Transport Queues**: async, async_redis_1, async_redis_2
- **70+ Message Handlers**: Xử lý webhooks, schedules, notifications
- **Health Checks**: K8s liveness/readiness probes

## 🚀 Quick Start

### 1. Build Worker Image
```bash
# Build locally for testing
docker build -f devops/worker/Dockerfile -t gn-worker-service .

# Run locally
docker run -d \
  --name gn-worker \
  -e APP_ENV=dev \
  -e DATABASE_URL=mysql://... \
  -e MESSENGER_TRANSPORT_DSN=redis://... \
  gn-worker-service
```

### 2. Deploy via GitLab CI
```bash
# Push to deploy branch
git checkout deploy/dev
git push origin deploy/dev

# Manually trigger in GitLab:
# 1. Go to CI/CD > Pipelines
# 2. Run "build_worker_image" job
# 3. Run "deploy_worker" job
```

### 3. Verify Deployment
```bash
# Check pods
kubectl get pods -l app=gn-worker-service

# Check logs
kubectl logs -l app=gn-worker-service --tail=50

# Check worker processes
kubectl exec -it <pod-name> -- supervisorctl status

# Check message queues
kubectl exec -it <pod-name> -- php bin/console messenger:stats
```

## 📁 File Structure

```
devops/worker/
├── Dockerfile              # Worker container definition
├── supervisord.conf         # Process management config
├── docker-entrypoint.sh     # Startup script
└── health-check.sh          # Health check script

.gitlab-ci/
└── deploy-worker-dev.yaml   # CI/CD pipeline

var/docs/
├── worker-requirements.md   # Technical requirements
└── worker-pipeline-setup.md # Deployment guide
```

## 🔧 Configuration

### Environment Variables
```env
# Required
DATABASE_URL=mysql://user:pass@host:port/db_name

# Redis Transports (K8s environment)
MESSENGER_TRANSPORT_DSN=redis://redis-service:6379/0
MESSENGER_TRANSPORT_REDIS_1_DSN=redis://redis-service:6379/1
MESSENGER_TRANSPORT_REDIS_2_DSN=redis://redis-service:6379/2

# Or external Redis (Google Cloud Memorystore)
# MESSENGER_TRANSPORT_DSN=redis://10.x.x.x:6379/0
# MESSENGER_TRANSPORT_REDIS_1_DSN=redis://10.x.x.x:6379/1
# MESSENGER_TRANSPORT_REDIS_2_DSN=redis://10.x.x.x:6379/2

# External APIs
BIRD_SMS_API_KEY=xxx
SMS_MODE_API_KEY=xxx
SH_API_KEY=xxx
SUMSUB_APP_TOKEN=xxx
# ... (see worker-requirements.md for full list)
```

### Supervisor Processes
- **messenger-async**: 2 processes (main queue)
- **messenger-redis-1**: 1 process (logger messages)
- **messenger-redis-2**: 1 process (heavy processing)

## 🔍 Monitoring

### Health Checks
```bash
# Manual health check
kubectl exec -it <pod> -- /usr/local/bin/health-check.sh

# Check supervisor status
kubectl exec -it <pod> -- supervisorctl status

# Restart processes
kubectl exec -it <pod> -- supervisorctl restart all
```

### Logs
```bash
# Application logs
kubectl logs -l app=gn-worker-service -f

# Supervisor logs
kubectl exec -it <pod> -- tail -f /var/log/supervisor/supervisord.log

# Worker process logs
kubectl exec -it <pod> -- tail -f /var/log/supervisor/messenger-async.out.log
```

### Message Queue Stats
```bash
# Check queue status
kubectl exec -it <pod> -- php bin/console messenger:stats

# Check failed messages
kubectl exec -it <pod> -- php bin/console messenger:failed:show

# Retry failed messages
kubectl exec -it <pod> -- php bin/console messenger:failed:retry
```

## 🔄 Operations

### Scaling
```bash
# Scale replicas
kubectl scale deployment gn-worker-service --replicas=3

# Auto-scaling
kubectl autoscale deployment gn-worker-service --cpu-percent=70 --min=2 --max=5
```

### Rollback
```bash
# Rollback deployment
kubectl rollout undo deployment/gn-worker-service

# Check rollout status
kubectl rollout status deployment/gn-worker-service
```

### Debugging
```bash
# Get pod shell
kubectl exec -it <pod-name> -- bash

# Check Redis connectivity for all transports
kubectl exec -it <pod> -- /usr/local/bin/health-check.sh

# Or manually check each transport
kubectl exec -it <pod> -- php -r "
echo 'Checking Redis transports:\n';
\$transports = ['MESSENGER_TRANSPORT_DSN', 'MESSENGER_TRANSPORT_REDIS_1_DSN', 'MESSENGER_TRANSPORT_REDIS_2_DSN'];
foreach (\$transports as \$name) {
    \$dsn = getenv(\$name);
    if (!\$dsn) { echo \$name . ': NOT SET\n'; continue; }
    \$parsed = parse_url(\$dsn);
    try {
        \$redis = new Redis();
        \$redis->connect(\$parsed['host'], \$parsed['port'] ?? 6379, 5);
        echo \$name . ': OK (' . \$parsed['host'] . ':' . (\$parsed['port'] ?? 6379) . ')\n';
    } catch (Exception \$e) {
        echo \$name . ': FAIL - ' . \$e->getMessage() . '\n';
    }
}
"

# Check database connectivity
kubectl exec -it <pod> -- php bin/console doctrine:query:sql "SELECT 1"
```

## 📊 Performance

### Resource Usage
- **CPU**: 512m request, 1000m limit
- **Memory**: 1Gi request, 2Gi limit
- **Replicas**: 2 (default), auto-scale to 5

### Message Processing
- **Throughput**: ~100 messages/second per worker
- **Latency**: <1 second for simple messages
- **Retry**: 3 attempts with exponential backoff

## 🚨 Troubleshooting

### Common Issues

#### Worker pods not starting
```bash
kubectl describe pod <pod-name>
kubectl get events --sort-by=.metadata.creationTimestamp
```

#### Messages not processing
```bash
# Check transport connectivity
kubectl exec -it <pod> -- php bin/console messenger:stats

# Check supervisor processes
kubectl exec -it <pod> -- supervisorctl status

# Restart workers
kubectl exec -it <pod> -- supervisorctl restart messenger-async:*
```

#### High memory usage
```bash
# Check memory usage
kubectl top pods -l app=gn-worker-service

# Restart workers to clear memory
kubectl rollout restart deployment/gn-worker-service
```

## 📚 Documentation

- **Technical Requirements**: `var/docs/worker-requirements.md`
- **Pipeline Setup**: `var/docs/worker-pipeline-setup.md`
- **Original Architecture**: See documentation header in this README

## 🎯 Next Steps

1. **Monitor Performance**: Watch resource usage and message throughput
2. **Optimize Scaling**: Adjust replicas based on load patterns
3. **Add Metrics**: Implement Prometheus metrics for better monitoring
4. **Alerting**: Setup alerts for worker failures and queue backlogs

## 🤝 Contributing

When making changes to worker service:

1. Update `devops/worker/` files as needed
2. Test locally with Docker
3. Update documentation
4. Deploy via GitLab CI pipeline
5. Monitor deployment and performance
