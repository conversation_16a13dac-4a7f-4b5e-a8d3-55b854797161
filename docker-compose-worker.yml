services:
  worker:
    build:
      context: .
      dockerfile: devops/worker/Dockerfile
    container_name: greennation-worker
    restart: always
    environment:
      - APP_ENV=dev
      - APP_DEBUG=1
      - DATABASE_URL=mysql://root:root@db:3306/greennation
      - MESSENGER_TRANSPORT_DSN=redis://redis:6379/0
      - MESSENGER_TRANSPORT_REDIS_1_DSN=redis://redis:6379/1
      - ME<PERSON>ENGER_TRANSPORT_REDIS_2_DSN=redis://redis:6379/2
      - BIRD_SMS_API_KEY=dummy
      - BIRD_SMS_API_URL=https://dummy.com
      - SMS_MODE_API_KEY=dummy
      - MTARGET_API_USERNAME=dummy
      - MTARGET_API_PASSWORD=dummy
      
      - SH_API_URL=https://dummy.com
      - SH_API_KEY=dummy
      - PLAID_CLIENT_ID=dummy
      - PLAID_SECRET=dummy
      
      - SUMSUB_APP_TOKEN=dummy
      - SUMSUB_SECRET_KEY=dummy
      - INCODE_API_KEY=dummy
      
      - GOOGLE_CLOUD_LOGGER_PROJECT_ID=green-nation-dev-5004
      
    volumes:
      - ./:/var/www/html
      - ./var/log:/var/www/html/var/log
    depends_on:
      - db
      - redis
    networks:
      - greennation-worker
  db:
    image: mysql:8.0
    container_name: greennation-worker-db
    command:
      - "--default-authentication-plugin=mysql_native_password"
      - "--max_allowed_packet=32505856"
    ports:
      - "33061:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: greennation
      MYSQL_USER: greennation
      MYSQL_PASSWORD: greennation
    volumes:
      - worker_db_data:/var/lib/mysql
    networks:
      - greennation-worker

  redis:
    image: redis:6.2-alpine
    container_name: greennation-worker-redis
    ports:
      - "6380:6379"
    volumes:
      - worker_redis_data:/data
    networks:
      - greennation-worker
    command: redis-server --appendonly yes

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: greennation-worker-redis-ui
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - greennation-worker

  web:
    build:
      context: .
      dockerfile: devops/ci/Dockerfile
    container_name: greennation-web-test
    ports:
      - "8080:80"
    environment:
      - APP_ENV=dev
      - APP_DEBUG=1
      - DATABASE_URL=mysql://root:root@db:3306/greennation
      - MESSENGER_TRANSPORT_DSN=redis://redis:6379/0
      - MESSENGER_TRANSPORT_REDIS_1_DSN=redis://redis:6379/1
      - MESSENGER_TRANSPORT_REDIS_2_DSN=redis://redis:6379/2
    volumes:
      - ./:/var/www/html
    depends_on:
      - db
      - redis
    networks:
      - greennation-worker

volumes:
  worker_db_data:
    driver: local
  worker_redis_data:
    driver: local

networks:
  greennation-worker:
    driver: bridge
