# Worker Service Dockerfile
# Dựa trên base image hi<PERSON><PERSON> tạ<PERSON>, c<PERSON>i đặt supervisor đ<PERSON> quản lý worker processes

FROM europe-west9-docker.pkg.dev/green-nation-dev-5004/appv2/gn-php-base-service:latest

# Install supervisor for process management
RUN apt-get update && apt-get install -y \
    supervisor \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy application code (same as web application)
COPY --chown=www-data:www-data . /var/www/html/

# Install PHP dependencies (production optimized)
RUN cd /var/www/html && composer install --no-interaction --optimize-autoloader --no-dev --no-scripts

# Create necessary directories for supervisor and logs
RUN mkdir -p /var/log/supervisor \
    && mkdir -p /var/www/html/var/cache \
    && mkdir -p /var/www/html/var/log \
    && chown -R www-data:www-data /var/www/html/var \
    && chown -R www-data:www-data /var/log/supervisor

# Copy supervisor configuration
COPY devops/worker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy worker entrypoint script
COPY devops/worker/docker-entrypoint.sh /usr/local/bin/worker-entrypoint.sh
RUN chmod +x /usr/local/bin/worker-entrypoint.sh

# Copy health check script
COPY devops/worker/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# Set working directory
WORKDIR /var/www/html/

# Worker không cần expose ports (chỉ consume messages)
# EXPOSE 80

# Health check để K8s biết worker đang hoạt động
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# Set proper user
USER www-data

# Start supervisor to manage worker processes
ENTRYPOINT ["/usr/local/bin/worker-entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf", "-n"]
