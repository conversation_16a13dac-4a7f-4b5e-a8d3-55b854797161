#!/bin/bash
set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

check_supervisor() {
    if pgrep -f supervisord > /dev/null; then
        log "✅ Supervisor is running"
        return 0
    else
        log "❌ Supervisor is not running"
        return 1
    fi
}

check_supervisor_processes() {
    local failed=0
    
    if ! command -v supervisorctl > /dev/null; then
        log "❌ supervisorctl not available"
        return 1
    fi
    
    local status_output
    status_output=$(supervisorctl status 2>/dev/null || echo "ERROR")
    
    if [ "$status_output" = "ERROR" ]; then
        log "❌ Cannot get supervisor status"
        return 1
    fi
    
    local required_processes=("messenger-async" "messenger-redis-1" "messenger-redis-2")
    
    for process in "${required_processes[@]}"; do
        if echo "$status_output" | grep -q "$process.*RUNNING"; then
            log "✅ $process is running"
        else
            log "❌ $process is not running"
            failed=1
        fi
    done
    
    return $failed
}

check_redis() {
    local transports=(
        "${MESSENGER_TRANSPORT_DSN}"
        "${MESSENGER_TRANSPORT_REDIS_1_DSN}"
        "${MESSENGER_TRANSPORT_REDIS_2_DSN}"
    )

    local failed=0

    for i in "${!transports[@]}"; do
        local redis_dsn="${transports[$i]}"
        local transport_name="transport_$((i+1))"

        if [ -z "$redis_dsn" ]; then
            log "⚠️  Redis DSN not set for $transport_name"
            continue
        fi

        local result=$(php -r "
        try {
            \$dsn = parse_url('$redis_dsn');
            if (!\$dsn || !isset(\$dsn['host'])) {
                echo 'INVALID_DSN';
                exit(1);
            }

            \$redis = new Redis();
            \$host = \$dsn['host'];
            \$port = \$dsn['port'] ?? 6379;
            \$db = isset(\$dsn['path']) ? ltrim(\$dsn['path'], '/') : 0;

            if (!\$redis->connect(\$host, \$port, 5)) {
                echo 'CONNECTION_FAILED';
                exit(1);
            }

            if (\$db > 0) {
                \$redis->select(\$db);
            }

            \$result = \$redis->ping();
            if (\$result === '+PONG' || \$result === 'PONG' || \$result === true) {
                echo 'OK';
            } else {
                echo 'PING_FAILED';
            }
        } catch (Exception \$e) {
            echo 'EXCEPTION: ' . \$e->getMessage();
        }
        " 2>/dev/null)

        if [ "$result" = "OK" ]; then
            log "✅ Redis $transport_name connectivity OK ($redis_dsn)"
        else
            log "❌ Redis $transport_name connectivity failed: $result ($redis_dsn)"
            failed=1
        fi
    done

    return $failed
}

check_database() {
    php /var/www/html/bin/console doctrine:query:sql "SELECT 1" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Database connectivity OK"
        return 0
    else
        log "❌ Database connectivity failed"
        return 1
    fi
}

check_messenger() {
    php /var/www/html/bin/console messenger:stats > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Messenger transports OK"
        return 0
    else
        log "❌ Messenger transports failed"
        return 1
    fi
}

check_memory() {
    local memory_usage
    memory_usage=$(ps aux | grep -E "(messenger:consume|php)" | grep -v grep | awk '{sum += $4} END {print sum}')
    
    if [ -z "$memory_usage" ]; then
        memory_usage=0
    fi
    
    if (( $(echo "$memory_usage < 80" | bc -l) )); then
        log "✅ Memory usage OK (${memory_usage}%)"
        return 0
    else
        log "⚠️  High memory usage (${memory_usage}%)"
        return 1
    fi
}

main() {
    log "🔍 Starting worker health check..."
    
    local exit_code=0
    
    check_supervisor || exit_code=1
    check_supervisor_processes || exit_code=1
    check_redis || exit_code=1
    
    check_database || log "⚠️  Database check failed (non-critical)"
    check_messenger || log "⚠️  Messenger check failed (non-critical)"
    check_memory || log "⚠️  Memory check failed (non-critical)"
    
    if [ $exit_code -eq 0 ]; then
        log "✅ Worker health check passed"
    else
        log "❌ Worker health check failed"
    fi
    
    return $exit_code
}

main "$@"
