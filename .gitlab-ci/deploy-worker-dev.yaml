variables:
  ## SYSTEM
  ENV: dev
  SERVICE_NAME: gn-worker-service
  SERVICE_CPU_REQ: 512m
  SERVICE_MEM_REQ: 1Gi
  GCLOUD_PROJECT: green-nation-dev-5004
  IMAGE_REPO: europe-west9-docker.pkg.dev
  IMAGE_NAME: ${IMAGE_REPO}/${GCLOUD_PROJECT}/appv2/${SERVICE_NAME}
  IMAGE_TAG: $ENV-$CI_COMMIT_SHA-$CI_PIPELINE_ID

default:
  tags:
    - dev_gke

build_worker_image:
  stage: build-image
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  before_script: []
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/devops/worker/Dockerfile"
      --destination "${IMAGE_NAME}:${IMAGE_TAG}"
      --snapshot-mode=redo
  rules:
    - if: '$CI_COMMIT_BRANCH == "deploy/worker-dev"'
      when: manual
    - if: "$CI_COMMIT_TAG"
      when: manual

deploy_worker:
  stage: deploy-dev
  image: google/cloud-sdk:latest
  variables:
    GIT_STRATEGY: none
  before_script: []
  script:
    - echo "Start Deploying Worker"
    - gcloud secrets versions access latest --secret=gitlab-runner-deploy-worker-dev --project="${GCLOUD_PROJECT}" > /tmp/deploy_script.sh
    - chmod +x /tmp/deploy_script.sh
    - /tmp/deploy_script.sh
  rules:
    - if: '$CI_COMMIT_BRANCH == "deploy/worker-dev"'
      when: manual
  needs: ["build_worker_image"]
