#!/bin/bash
set -e

echo "🚀 Starting Worker Service..."

echo "📁 Creating directories..."
mkdir -p /var/www/html/var/cache
mkdir -p /var/www/html/var/log
mkdir -p /var/log/supervisor

echo "🔐 Setting permissions..."
chown -R www-data:www-data /var/www/html/var
chmod -R 755 /var/www/html/var/cache
chmod -R 755 /var/www/html/var/log

chown -R www-data:www-data /var/log/supervisor
chmod -R 755 /var/log/supervisor

echo "🧹 Clearing Symfony cache..."
if [ "$APP_ENV" = "prod" ]; then
    php /var/www/html/bin/console cache:clear --env=prod --no-debug
else
    php /var/www/html/bin/console cache:clear --env=dev
fi

echo "🔥 Warming up cache..."
if [ "$APP_ENV" = "prod" ]; then
    php /var/www/html/bin/console cache:warmup --env=prod --no-debug
else
    php /var/www/html/bin/console cache:warmup --env=dev
fi

echo "🔍 Checking database connectivity..."
php /var/www/html/bin/console doctrine:query:sql "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

echo "🔍 Checking Redis connectivity..."

REDIS_TRANSPORTS=(
    "MESSENGER_TRANSPORT_DSN"
    "MESSENGER_TRANSPORT_REDIS_1_DSN"
    "MESSENGER_TRANSPORT_REDIS_2_DSN"
)

for transport_var in "${REDIS_TRANSPORTS[@]}"; do
    transport_dsn=$(printenv "$transport_var")

    if [ -z "$transport_dsn" ]; then
        echo "⚠️  $transport_var not set, skipping..."
        continue
    fi

    echo "Checking $transport_var: $transport_dsn"

    php -r "
    try {
        \$dsn = parse_url('$transport_dsn');
        if (!\$dsn || !isset(\$dsn['host'])) {
            throw new Exception('Invalid DSN format');
        }

        \$redis = new Redis();
        \$host = \$dsn['host'];
        \$port = \$dsn['port'] ?? 6379;
        \$db = isset(\$dsn['path']) ? ltrim(\$dsn['path'], '/') : 0;

        if (!\$redis->connect(\$host, \$port, 10)) {
            throw new Exception('Connection failed to ' . \$host . ':' . \$port);
        }

        if (\$db > 0) {
            \$redis->select(\$db);
        }

        \$result = \$redis->ping();
        if (\$result === '+PONG' || \$result === 'PONG' || \$result === true) {
            echo '✅ ' . '$transport_var' . ' connection successful\n';
        } else {
            throw new Exception('Ping failed');
        }
    } catch (Exception \$e) {
        echo '❌ ' . '$transport_var' . ' connection failed: ' . \$e->getMessage() . '\n';
        exit(1);
    }
    "
done

echo "🔍 Checking messenger transports..."
php /var/www/html/bin/console messenger:stats > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Messenger transports ready"
else
    echo "❌ Messenger transports not ready"
    exit 1
fi

echo "📊 Worker Configuration:"
echo "  - APP_ENV: ${APP_ENV:-dev}"
echo "  - PHP Memory Limit: $(php -r 'echo ini_get("memory_limit");')"
echo "  - Available transports:"
php /var/www/html/bin/console messenger:stats 2>/dev/null | grep -E "async|redis" || echo "    No transport info available"

echo "📋 Supervisor will manage these processes:"
echo "  - messenger-async (2 processes)"
echo "  - messenger-redis-1 (1 process)"
echo "  - messenger-redis-2 (1 process)"

echo "✅ Worker initialization complete!"
echo "🎯 Starting supervisor..."

exec "$@"
