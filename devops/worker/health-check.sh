#!/bin/bash
# Worker Health Check Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if supervisor is running
check_supervisor() {
    if pgrep -f supervisord > /dev/null; then
        log "✅ Supervisor is running"
        return 0
    else
        log "❌ Supervisor is not running"
        return 1
    fi
}

# Function to check supervisor processes
check_supervisor_processes() {
    local failed=0
    
    # Check if supervisorctl is available
    if ! command -v supervisorctl > /dev/null; then
        log "❌ supervisorctl not available"
        return 1
    fi
    
    # Get process status
    local status_output
    status_output=$(supervisorctl status 2>/dev/null || echo "ERROR")
    
    if [ "$status_output" = "ERROR" ]; then
        log "❌ Cannot get supervisor status"
        return 1
    fi
    
    # Check each required process
    local required_processes=("messenger-async" "messenger-redis-1" "messenger-redis-2")
    
    for process in "${required_processes[@]}"; do
        if echo "$status_output" | grep -q "$process.*RUNNING"; then
            log "✅ $process is running"
        else
            log "❌ $process is not running"
            failed=1
        fi
    done
    
    return $failed
}

# Function to check Redis connectivity
check_redis() {
    local redis_dsn="${MESSENGER_TRANSPORT_DSN:-redis://localhost:6379}"
    
    php -r "
    try {
        \$redis = new Redis();
        \$dsn = parse_url('$redis_dsn');
        \$host = \$dsn['host'] ?? 'localhost';
        \$port = \$dsn['port'] ?? 6379;
        \$redis->connect(\$host, \$port, 5); // 5 second timeout
        \$result = \$redis->ping();
        if (\$result === '+PONG' || \$result === 'PONG' || \$result === true) {
            echo 'OK';
        } else {
            echo 'FAIL';
        }
    } catch (Exception \$e) {
        echo 'FAIL';
    }
    " 2>/dev/null | grep -q "OK"
    
    if [ $? -eq 0 ]; then
        log "✅ Redis connectivity OK"
        return 0
    else
        log "❌ Redis connectivity failed"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    php /var/www/html/bin/console doctrine:query:sql "SELECT 1" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Database connectivity OK"
        return 0
    else
        log "❌ Database connectivity failed"
        return 1
    fi
}

# Function to check messenger transports
check_messenger() {
    php /var/www/html/bin/console messenger:stats > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Messenger transports OK"
        return 0
    else
        log "❌ Messenger transports failed"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    local memory_usage
    memory_usage=$(ps aux | grep -E "(messenger:consume|php)" | grep -v grep | awk '{sum += $4} END {print sum}')
    
    if [ -z "$memory_usage" ]; then
        memory_usage=0
    fi
    
    # Check if memory usage is reasonable (less than 80%)
    if (( $(echo "$memory_usage < 80" | bc -l) )); then
        log "✅ Memory usage OK (${memory_usage}%)"
        return 0
    else
        log "⚠️  High memory usage (${memory_usage}%)"
        return 1
    fi
}

# Main health check function
main() {
    log "🔍 Starting worker health check..."
    
    local exit_code=0
    
    # Critical checks (failure means unhealthy)
    check_supervisor || exit_code=1
    check_supervisor_processes || exit_code=1
    check_redis || exit_code=1
    
    # Non-critical checks (warnings only)
    check_database || log "⚠️  Database check failed (non-critical)"
    check_messenger || log "⚠️  Messenger check failed (non-critical)"
    check_memory || log "⚠️  Memory check failed (non-critical)"
    
    if [ $exit_code -eq 0 ]; then
        log "✅ Worker health check passed"
    else
        log "❌ Worker health check failed"
    fi
    
    return $exit_code
}

# Run health check
main "$@"
